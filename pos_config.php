<?php
/**
 * Configuration POS - Système de Point de Vente
 * Basé sur votre solution HFSQL qui fonctionne
 */

// Configuration de la base de données (votre solution qui marche)
define('POS_DSN', 'odbc:DataCafe');
define('POS_USERNAME', 'admin');
define('POS_PASSWORD', '');

// Configuration du POS
define('POS_NAME', 'BeCoffe POS');
define('POS_VERSION', '1.0');
define('POS_CURRENCY', '€');
define('POS_TAX_RATE', 0.20); // 20% TVA

// Configuration des sessions
session_start();

/**
 * Classe POS Manager - Gestion complète du point de vente
 */
class POSManager {
    private $pdo;
    private $connected = false;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $this->pdo = new PDO(POS_DSN, POS_USERNAME, POS_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 30
            ]);
            $this->connected = true;
        } catch (PDOException $e) {
            $this->connected = false;
            error_log("Erreur connexion POS: " . $e->getMessage());
        }
    }
    
    public function isConnected() {
        return $this->connected;
    }
    
    /**
     * Récupérer toutes les catégories pour le menu
     */
    public function getCategories() {
        if (!$this->connected) return [];
        
        try {
            $sql = "SELECT IDCategorie, categories, photo FROM Categorie ORDER BY categories";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Erreur getCategories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les articles d'une catégorie
     */
    public function getArticlesByCategory($categoryId = null, $includeZeroStock = true) {
        if (!$this->connected) return [];

        try {
            $stockCondition = $includeZeroStock ? "" : "AND a.quantite > 0";

            if ($categoryId) {
                $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix, c.categories as nom_categorie
                        FROM articles a
                        LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                        WHERE a.IDCategorie = ? $stockCondition
                        ORDER BY a.designation";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$categoryId]);
            } else {
                $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix, c.categories as nom_categorie
                        FROM articles a
                        LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                        WHERE 1=1 $stockCondition
                        ORDER BY c.categories, a.designation";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute();
            }
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Erreur getArticlesByCategory: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Rechercher des articles
     */
    public function searchArticles($search, $includeZeroStock = true) {
        if (!$this->connected) return [];

        try {
            $stockCondition = $includeZeroStock ? "" : "AND a.quantite > 0";

            $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix, c.categories as nom_categorie
                    FROM articles a
                    LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                    WHERE a.designation LIKE ? $stockCondition
                    ORDER BY a.designation";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['%' . $search . '%']);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Erreur searchArticles: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Gestion du panier de commande
     */
    public function addToCart($articleId, $quantity = 1, $allowInsufficientStock = true) {
        if (!isset($_SESSION['pos_cart'])) {
            $_SESSION['pos_cart'] = [];
        }

        // Vérifier si l'article existe
        $article = $this->getArticleById($articleId);
        if (!$article) {
            return ['success' => false, 'message' => 'Article introuvable'];
        }

        // Vérifier le stock seulement si la validation est activée
        if (!$allowInsufficientStock && $article['quantite'] < $quantity) {
            return ['success' => false, 'message' => 'Stock insuffisant'];
        }

        // Déterminer si le stock est insuffisant pour l'affichage
        $isStockInsufficient = $article['quantite'] < $quantity;

        // Ajouter au panier
        if (isset($_SESSION['pos_cart'][$articleId])) {
            $_SESSION['pos_cart'][$articleId]['quantity'] += $quantity;
        } else {
            $_SESSION['pos_cart'][$articleId] = [
                'article' => $article,
                'quantity' => $quantity,
                'price' => $this->getArticlePrice($articleId),
                'stock_insufficient' => $isStockInsufficient
            ];
        }

        // Retourner le résultat avec information sur le stock
        $message = $isStockInsufficient ? 'Article ajouté (stock insuffisant)' : 'Article ajouté';
        return ['success' => true, 'message' => $message, 'stock_insufficient' => $isStockInsufficient];
    }
    
    /**
     * Récupérer un article par ID
     */
    public function getArticleById($id) {
        if (!$this->connected) return null;
        
        try {
            $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix, c.categories as nom_categorie
                    FROM articles a
                    LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                    WHERE a.IDarticles = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Erreur getArticleById: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obtenir le prix d'un article
     */
    public function getArticlePrice($articleId) {
        // 1. Récupérer l'article avec son prix de la base de données
        $article = $this->getArticleById($articleId);
        if ($article && isset($article['prix']) && $article['prix'] > 0) {
            return floatval($article['prix']);
        }

        // 2. Chargement des prix depuis un fichier JSON (override)
        $pricesFile = 'prices.json';
        if (file_exists($pricesFile)) {
            $prices = json_decode(file_get_contents($pricesFile), true) ?? [];
            if (isset($prices[$articleId])) {
                return floatval($prices[$articleId]);
            }
        }

        // 3. Prix par défaut basés sur les catégories (fallback)
        if ($article) {
            $defaultPrices = [
                1 => 2.50,  // Boissons chaudes
                2 => 2.00,  // Boissons froides
                3 => 4.50,  // Pâtisseries
                4 => 5.50,  // Sandwichs
                5 => 4.00,  // Salades
            ];

            return $defaultPrices[$article['IDCategorie']] ?? 3.00;
        }

        return 3.00; // Prix par défaut
    }
    
    /**
     * Récupérer le contenu du panier
     */
    public function getCart() {
        return $_SESSION['pos_cart'] ?? [];
    }
    
    /**
     * Calculer le total du panier
     */
    public function getCartTotal() {
        $cart = $this->getCart();
        $total = 0;
        
        foreach ($cart as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        
        return $total;
    }
    
    /**
     * Calculer la TVA
     */
    public function getCartTax() {
        return $this->getCartTotal() * POS_TAX_RATE;
    }
    
    /**
     * Total TTC
     */
    public function getCartTotalTTC() {
        return $this->getCartTotal() + $this->getCartTax();
    }
    
    /**
     * Vider le panier
     */
    public function clearCart() {
        $_SESSION['pos_cart'] = [];
    }
    
    /**
     * Supprimer un article du panier
     */
    public function removeFromCart($articleId) {
        if (isset($_SESSION['pos_cart'][$articleId])) {
            unset($_SESSION['pos_cart'][$articleId]);
            return true;
        }
        return false;
    }
    
    /**
     * Modifier la quantité d'un article dans le panier
     */
    public function updateCartQuantity($articleId, $quantity) {
        if (isset($_SESSION['pos_cart'][$articleId])) {
            if ($quantity <= 0) {
                $this->removeFromCart($articleId);
            } else {
                // Vérifier le stock pour mettre à jour l'indicateur
                $article = $this->getArticleById($articleId);
                $isStockInsufficient = $article && $article['quantite'] < $quantity;

                $_SESSION['pos_cart'][$articleId]['quantity'] = $quantity;
                $_SESSION['pos_cart'][$articleId]['stock_insufficient'] = $isStockInsufficient;
            }
            return true;
        }
        return false;
    }
    
    /**
     * Finaliser la commande (à développer selon vos besoins)
     */
    public function processOrder($paymentMethod = 'cash') {
        $cart = $this->getCart();
        if (empty($cart)) {
            return false;
        }
        
        // Ici vous pouvez :
        // 1. Enregistrer la commande dans une table "commandes"
        // 2. Décrémenter le stock des articles
        // 3. Imprimer le ticket
        // 4. Envoyer en cuisine si nécessaire
        
        $orderId = $this->saveOrder($cart, $paymentMethod);
        
        if ($orderId) {
            $this->clearCart();
            return $orderId;
        }
        
        return false;
    }
    
    /**
     * Sauvegarder la commande (exemple basique)
     */
    private function saveOrder($cart, $paymentMethod) {
        // Exemple de sauvegarde - à adapter selon votre structure de base
        try {
            $orderId = 'CMD_' . date('YmdHis') . '_' . rand(1000, 9999);
            
            // Ici vous pourriez insérer dans une table "commandes"
            // Pour l'instant, on sauvegarde dans un fichier JSON
            $order = [
                'id' => $orderId,
                'timestamp' => date('Y-m-d H:i:s'),
                'items' => $cart,
                'total_ht' => $this->getCartTotal(),
                'tax' => $this->getCartTax(),
                'total_ttc' => $this->getCartTotalTTC(),
                'payment_method' => $paymentMethod,
                'status' => 'completed'
            ];
            
            $ordersFile = 'orders/' . date('Y-m-d') . '.json';
            if (!is_dir('orders')) {
                mkdir('orders', 0755, true);
            }
            
            $orders = [];
            if (file_exists($ordersFile)) {
                $orders = json_decode(file_get_contents($ordersFile), true) ?? [];
            }
            
            $orders[] = $order;
            file_put_contents($ordersFile, json_encode($orders, JSON_PRETTY_PRINT));
            
            return $orderId;
            
        } catch (Exception $e) {
            error_log("Erreur saveOrder: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Statistiques du jour
     */
    public function getTodayStats() {
        $ordersFile = 'orders/' . date('Y-m-d') . '.json';
        
        if (!file_exists($ordersFile)) {
            return [
                'orders_count' => 0,
                'total_revenue' => 0,
                'items_sold' => 0
            ];
        }
        
        $orders = json_decode(file_get_contents($ordersFile), true) ?? [];
        
        $stats = [
            'orders_count' => count($orders),
            'total_revenue' => 0,
            'items_sold' => 0
        ];
        
        foreach ($orders as $order) {
            $stats['total_revenue'] += $order['total_ttc'];
            foreach ($order['items'] as $item) {
                $stats['items_sold'] += $item['quantity'];
            }
        }
        
        return $stats;
    }
}

// Initialisation globale du POS
$pos = new POSManager();

// Fonctions utilitaires
function formatPrice($price) {
    return number_format($price, 2, ',', ' ') . ' ' . POS_CURRENCY;
}

function formatDateTime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}
?>
