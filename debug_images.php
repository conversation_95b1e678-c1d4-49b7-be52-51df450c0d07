<?php
require_once 'pos_config.php';

echo "<h1>Debug Images Articles</h1>";

// Récupérer quelques articles pour voir les données
$articles = $pos->getArticlesByCategory();

echo "<h2>Données des articles (premiers 10) :</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Nom</th><th>Prix</th><th>Image (champ)</th><th>Image existe?</th><th>Aperçu</th></tr>";

$count = 0;
foreach ($articles as $article) {
    if ($count >= 10) break;
    
    $imageExists = !empty($article['image']) && file_exists($article['image']);
    $imageDisplay = !empty($article['image']) ? htmlspecialchars($article['image']) : 'VIDE';
    
    echo "<tr>";
    echo "<td>" . $article['IDarticles'] . "</td>";
    echo "<td>" . htmlspecialchars($article['designation']) . "</td>";
    echo "<td>" . (isset($article['prix']) ? $article['prix'] : 'N/A') . "</td>";
    echo "<td>" . $imageDisplay . "</td>";
    echo "<td>" . ($imageExists ? 'OUI' : 'NON') . "</td>";
    echo "<td>";
    if ($imageExists) {
        echo "<img src='" . htmlspecialchars($article['image']) . "' style='width: 50px; height: 50px; object-fit: cover;'>";
    } else {
        echo "Pas d'image";
    }
    echo "</td>";
    echo "</tr>";
    
    $count++;
}

echo "</table>";

// Vérifier la structure de la table
echo "<h2>Structure de la table articles :</h2>";
try {
    $stmt = $pos->pdo->query("SELECT TOP 1 * FROM articles");
    $article = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($article) {
        echo "<pre>";
        print_r(array_keys($article));
        echo "</pre>";
        
        echo "<h3>Exemple d'article complet :</h3>";
        echo "<pre>";
        print_r($article);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "Erreur : " . $e->getMessage();
}
?>
